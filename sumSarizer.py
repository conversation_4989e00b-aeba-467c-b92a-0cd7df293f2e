import numpy as np
import pandas as pd

import seaborn as sns
import matplotlib.pyplot as plt

from xgboost import XGBClassifier
import rle


## help functions
def get_sample_interval(data, colName = "Date/Time"):
    if 'sample_interval' in data.columns:
        return data['sample_interval'].iloc[0]
    else:
        return (sorted(pd.to_datetime(data[colName]).unique())[1] - sorted(pd.to_datetime(data[colName]).unique())[0]) / np.timedelta64(1, 's')

def loadModel(modelPath):
    bst = XGBClassifier(n_estimators=2, max_depth=2, learning_rate=1)
    bst.load_model(modelPath)
    return bst

def runQuantile(col, k, prob=0.8):
    quantile_difftemps = []
    for i in range(len(col)):
        if i <= (len(col)-k):
            quantile_difftemps.append(np.quantile(col[i:(i+k)], 0.8))
    quantile_difftemps.extend([None] * (k-1))
    return quantile_difftemps

def smoothEvents(label, sample_interval, min_event_sec = 5*60, min_break_sec = 30 * 60):
    #remove short breaks between cooking
    rleLabel1, rleLength1 = rle.encode(label)
    rleDF1 = pd.DataFrame(np.transpose([rleLabel1, rleLength1]), columns=['label', 'length'])
    selectedRows = (rleDF1['label'] == 0) & (rleDF1['length']*sample_interval < min_break_sec)
    rleDF1.loc[selectedRows.to_list(), 'label'] = 1
    rleDF1['label'] = rleDF1['label'].astype(bool)

    newLabels1 = []
    for idx, row in rleDF1.iterrows():
        newLabels1.extend([row['label']] * row['length'])
    
    #remove short cooking events
    rleLabel2, rleLength2 = rle.encode(newLabels1)

    rleDF2 = pd.DataFrame(np.transpose([rleLabel2, rleLength2]), columns=['label', 'length'])
    selectedRows = ((rleDF2['label'] == 1) & (rleDF2['length']*sample_interval < min_event_sec)) | ((rleDF2['label'] == 1) & (rleDF2['length'] < 2))
    rleDF2.loc[selectedRows, 'label'] = 0
    rleDF2['label'] = rleDF2['label'].astype(bool)
    
    newLabels2 = []
    for idx, row in rleDF2.iterrows():
        newLabels2.extend([row['label']] * row['length'])
        
    return newLabels2

def firefinder_detector(data, 
                        primary_threshold=75,
                        min_event_temp=None, 
                        min_event_sec=5*60, 
                        min_break_sec=30*60):
    #' Detect Cooking Using Firefinder Algorithm
    #' 
    #' TODO: Danny document this 
    #' 
    #' @param data a sumsarizer formatted data table for one sensor mission
    #' @param primary_threshold the main threshold to determine cooking
    #' @param min_event_temp min temperature for an event
    #' @param min_event_sec min number of seconds in a real event
    #' @param min_break_sec min number of seconds to makeup a real break
    max_run_length = 100
    
    # calculate features
    sample_interval = get_sample_interval(data)
    sample_interval_mins = sample_interval/60
    
    dataCopy = copy.deepcopy(data)
    dataCopy['Date/Time'] = pd.to_datetime(dataCopy["Date/Time"])
    #make a column of 1st derivative (degC/minute)
    dataCopy['difftemps'] = dataCopy['Value'].diff()
    
    #make a column of delta timestamps
    dataCopy['difftimes'] = pd.to_datetime(dataCopy["Date/Time"]).diff() / np.timedelta64(1, 's')
    
    #look at whether or not most of the data coming up in the next
    #hour is negative slope or 100 data points, whichever is lower
    dataCopy['quantile_difftemps'] = runQuantile(dataCopy['difftemps'], 
                                                 k=min(max_run_length, round(60/sample_interval_mins)))
    
    ##### RUN THE DECISION TREE 
    
    #define points that are likely to be cooking
    dataCopy['label'] = dataCopy['Value']>primary_threshold
    
    #get rid of long runs of negative slopes
    selectedRows = dataCopy['quantile_difftemps'] < 0
    dataCopy.loc[selectedRows, 'label'] = False
    
    #assume cooking for highly positive slopes
    selectedRows = dataCopy['difftemps'] > 2
    dataCopy.loc[selectedRows, 'label'] = True
    
    #get rid of highly negative slopes
    selectedRows = [d < (-1*v/500) for d,v in zip(dataCopy['difftemps'], dataCopy['Value'])]
    dataCopy.loc[selectedRows, 'label'] = False
    
    #remove places with gaps longer than the sample interval
    selectedRows = dataCopy['difftimes'] > sample_interval
    dataCopy.loc[selectedRows, 'label'] = False
    
    
    dataCopy['label'] = smoothEvents(dataCopy['label'], sample_interval, min_event_sec, min_break_sec)
    dataCopy['label'] = smoothEvents(dataCopy['label'], sample_interval, min_event_sec, min_break_sec)
#     dataCopy['label'] = labelNew
    
    #remove events with very low cooking temps
    if min_event_temp != None:
        selectedRows = dataCopy['Value'] < min_event_temp
        dataCopy.loc[selectedRows, 'label'] = False
    
    #remove events for data that is out of range and is probably an error
    selectedRows = (dataCopy['Value'] > 1000) | (dataCopy['Value'] < -50)
    dataCopy.loc[selectedRows, 'label'] = False
    
    return dataCopy['label']

def threshold_detector(data, threshold=85, direction=">"):
    #' Detect Cooking Using Simple Threshold
    #' 
    #' Detects cooking using a simple threshold. 
    #' @param data a sumsarizer formatted data table for one sensor mission
    #' @param threshold the main threshold to determine cooking
    #' @param direction one of > < >= or <=
    #' @param ... not currently used
    sample_interval = get_sample_interval(data)
    if direction not in ['>', '<', '>=', '<=']:
        print("direction must be one of '>', '<', '>=', '<='")
        return None
    if direction == '>':
        return smoothEvents(data['Value'] > threshold, sample_interval)
    elif direction == '<':
        return smoothEvents(data['Value'] < threshold, sample_interval)
    elif direction == '<=':
        return smoothEvents(data['Value'] <= threshold, sample_interval)
    else:
        return smoothEvents(data['Value'] >= threshold, sample_interval)

def ml_detector(data, model_path, threshold=0.5):
    #' Use sl3 Machine Learning for event detection
    #' 
    #' Uses a sl3 machine learner model trained on labels from TRAINSET to detect events. 
    #' See TODO to train your own
    #' @param data a sumsarizer formatted data table for one sensor mission
    #' @param model_obj either a sl3 learner fit object, or a url or path to a .rdata file containing one
    #' @param threshold a value between 0 and 1 indicating the sensitivity of the event detector 
    if not os.path.exists(model_path):
        print("Model file {} could not be found".format(model_path))
    # load model
    sample_interval = get_sample_interval(data)
    bst = loadModel(model_path)
    pred = bst.predict(data['Value']) > 0
    pred = smoothEvents(pred, sample_interval)
    return pred

def formatEvents(data, filePath, labelCol='predML'):
  rleLabel1, rleLength1 = rle.encode(data[labelCol])

  count = 1
  events = []
  for i in range(len(rleLabel1)):
      if rleLabel1[i]:
          startIdx = sum(rleLength1[:i])
          endIdx = sum(rleLength1[:i+1])
          tempDF = data.iloc[startIdx:endIdx]
          t = pd.to_datetime(tempDF["Date/Time"])
  #         .diff() / np.timedelta64(1, 's')
          events.append({
              'event_num': count, 
              'start_time': tempDF['Date/Time'].iloc[0], 
              'stop_time': tempDF['Date/Time'].iloc[-1], 
              'min_temp': tempDF['Value'].min(), 
              'max_temp': tempDF['Value'].max(), 
              'duration_mins': (t.iloc[-1] - t.iloc[0]) / np.timedelta64(1, 's') / 60
          })
          count += 1  
  eventsDF = pd.DataFrame(events)
  eventsDF['filename'] = filePath.split('/')[-1].split('.')[0]
  return eventsDF


sns.set_theme()
def getEventsPlots(df, labelCol='pred'):
    events = []
    rleLabel1, rleLength1 = rle.encode(df[labelCol])
    for i in range(len(rleLabel1)-1):
        if rleLabel1[i]:
            startIdx = sum(rleLength1[:i])
            endIdx = sum(rleLength1[:i+1])
            events.append((startIdx, endIdx))
    return events

def getXtickLabel(se):
    ticks, labels = [], []
    se = pd.to_datetime(se)
    for i in range(len(se)-1):
        if (se[i].hour == 23) and (se[i+1].hour == 0):
            ticks.append(i+1)
            labels.append(se[i].date())
    return ticks, labels

def splitPlot(data, valueCol='value', labelCol='pred', figureTitle='', figurePath='', showPlot=True):
    maxCol = 1000
    splitChunk = np.ceil(len(data) / 1000).astype(int)
    ylimMax = data[valueCol].max() + 10
    fig, ax = plt.subplots(splitChunk, 1, figsize=(16,2*splitChunk), constrained_layout=True)
    for i in range(splitChunk):
        if i != splitChunk:
            tempDF = data.iloc[i*maxCol: (i+1)*maxCol].reset_index(drop=True)
        else:
            tempDF = data.iloc[i*maxCol: ].reset_index(drop=True)
        if splitChunk > 1:
          t = sns.lineplot(data=tempDF, x=np.arange(len(tempDF)), y=valueCol, ax=ax[i], color='black')
  #         for argi in tempDF[labelCol][tempDF[labelCol]].index:
  #             t.axvline(argi, color='red', linewidth=0.3, fillstyle='full')
          events = getEventsPlots(tempDF, labelCol)
          for e in events:
              ax[i].axvspan(e[0], e[1], alpha=0.5, color='red')
        else:
          t = sns.lineplot(data=tempDF, x=np.arange(len(tempDF)), y=valueCol, ax=ax, color='black')
  #         for argi in tempDF[labelCol][tempDF[labelCol]].index:
  #             t.axvline(argi, color='red', linewidth=0.3, fillstyle='full')
          events = getEventsPlots(tempDF, labelCol)
          for e in events:
              ax.axvspan(e[0], e[1], alpha=0.5, color='red')
        xticks, xlabels = getXtickLabel(tempDF['Date/Time'])
        t.set_xticks(xticks, xlabels)
        t.set_ylim(0,ylimMax)
    if len(figureTitle) > 0:
        plt.suptitle(figureTitle)
    if showPlot:
        plt.show()
    if len(figurePath) > 0:
        fig.savefig(figurePath, dpi=300, bbox_inches='tight')