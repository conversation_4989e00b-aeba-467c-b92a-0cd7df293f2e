import numpy as np
import pandas as pd
import os

import sumSarizer
import copy
import rle

from sklearn.model_selection import train_test_split
from sklearn.metrics import r2_score

import matplotlib.pyplot as plt
import seaborn as sns

import shap
from tqdm import tqdm

from IPython.core.display import HTML

from datetime import datetime

from psychrochart import PsychroChart

from metpy.calc import mixing_ratio_from_relative_humidity
from metpy.units import units
import math
import psychrolib

import southwark

import statsmodels.api as sm
import statsmodels.formula.api as smf
from statsmodels.stats.sandwich_covariance import cov_cluster
from scipy.stats import pearsonr

import plotly.graph_objs as go


# load data
data = pd.read_csv('../data/mergedData.csv')

# calculate temp difference
data['Temp_diff'] = data['TEMP_sensor'] - data['TEMP_station']

# remove fault sensor data
data = data[data['sensorID'] != 'BL007-52']

# export data
data.to_csv('../data/mergedData_clean.csv', index=False)

data = pd.read_csv('../data/mergedData_clean.csv')
data.head()

# pearson correlation for indoor and outdoor temperature and p-value
data = data.dropna(subset=['TEMP_sensor', 'TEMP_station'])
corr, p_value = pearsonr(data['TEMP_sensor'], data['TEMP_station'])
print("Pearson correlation coefficient:", corr)
print("P-value:", p_value)

# mean temperature by IMD
data.groupby('IMD')['Temp_diff'].mean().sort_values()

data.columns

# mean temperature by EPC
data.groupby(' EPCRating')['Temp_diff'].mean().sort_values()

data['Temp_diff'].describe()

data.head(2)

# filter data range: between 2023-09-04 and 2023-09-10 or
data['Date'] = pd.to_datetime(data['TIME_station'])
data_filtered = data[(data['Date'] >= '2023-09-04') & (data['Date'] <= '2023-09-10')]
data_filtered = data_filtered.reset_index(drop=True)


# filter data range: between 2023-09-04 and 2023-09-10 or between 2023-08-22 and 2023-08-24
data_filtered = data[((data['Date'] >= '2023-08-22') & (data['Date'] <= '2023-08-24')) | ((data['Date'] >= '2023-09-04') & (data['Date'] <= '2023-09-10'))]
data_filtered = data_filtered.reset_index(drop=True)
data_filtered['TEMP_station'].describe()

data_filtered.groupby('sensorID').mean()['Temp_diff'].describe()

fig, ax = plt.subplots(figsize=(20, 10))

# sort the data by sensorID
# data = data_filtered.sort_values(by='sensorID')
# boxplot of temperature averaged by hour for each sensorID
data_filtered['hour'] = data_filtered['Date'].dt.hour
data_filtered['sensorID'] = data_filtered['sensorID'].astype(str)
data_filtered = data_filtered.groupby(['sensorID', 'hour']).mean(numeric_only=True).reset_index()
sns.boxplot(data=data_filtered, x='sensorID', y='Temp_diff', ax=ax)

# Rotate x-axis tick labels by 45 degrees
plt.xticks(rotation=45)
ax.spines['top'].set_visible(False)
ax.spines['right'].set_visible(False)
ax.spines['left'].set_visible(False)
ax.spines['bottom'].set_visible(False)
# make the font size of x-axis and y-axis labels larger
ax.tick_params(axis='x', labelsize=16)
ax.tick_params(axis='y', labelsize=16)

plt.show()

fig.savefig('../figures/tempDiff_sensor_boxplot_heatwave_hourly_average.svg', dpi=300)

fig, ax = plt.subplots(figsize=(20, 10))

# sort the data by sensorID
data = data.sort_values(by=' EPCRating')
sns.boxplot(data=data.dropna(subset=[' EPCRating']), x='sensorID', y='Temp_diff', ax=ax, hue=' EPCRating')

# Rotate x-axis tick labels by 45 degrees
plt.xticks(rotation=45)

plt.show()

fig.savefig('../results/temp_diff_boxplot_EPC.svg', dpi=300)

data.describe()

EPC_count = data.drop_duplicates(subset=['sensorID'])[' EPCRating'].value_counts().reset_index()

EPC_count

fig, ax = plt.subplots(figsize=(6, 6))

# sort the data by sensorID
data = data.sort_values(by=' EPCRating')
sns.barplot(data=EPC_count, x=' EPCRating', y='count', ax=ax)

# Rotate x-axis tick labels by 45 degrees
# plt.xticks(rotation=45)
ax.spines['top'].set_visible(False)
ax.spines['right'].set_visible(False)
ax.spines['left'].set_visible(False)
ax.spines['bottom'].set_visible(False)

plt.show()

fig.savefig('../figures/EPCDis.png', dpi=300)

IMD_count = data.drop_duplicates(subset=['sensorID'])['IMD'].value_counts().reset_index()

IMD_count

fig, ax = plt.subplots(figsize=(6, 6))

# sort the data by sensorID
data = data.sort_values(by='IMD')
sns.barplot(data=IMD_count, x='IMD', y='count', ax=ax)

# Rotate x-axis tick labels by 45 degrees
# plt.xticks(rotation=45)
ax.spines['top'].set_visible(False)
ax.spines['right'].set_visible(False)
ax.spines['left'].set_visible(False)
ax.spines['bottom'].set_visible(False)

plt.show()

fig.savefig('../figures/IMDDis.png', dpi=300)

data.columns

tenure_count = data.drop_duplicates(subset=['sensorID'])['Tenures'].value_counts().reset_index()
tenure_count

housingType_count = data.drop_duplicates(subset=['sensorID'])['housingType'].value_counts().reset_index()
housingType_count

houseCondition_count = data.drop_duplicates(subset=['sensorID'])['selfRatedHousingCondition'].value_counts().reset_index()
houseCondition_count

# load data
data = pd.read_csv('../data/mergedData_clean.csv')
data['Date'] = pd.to_datetime(data['TIME_station'])
# filter data range: between 2023-09-04 and 2023-09-10 or between 2023-08-22 and 2023-08-24
data['HeatwavePeriod'] = ((data['Date'] >= '2023-08-22') & (data['Date'] <= '2023-08-24')) | ((data['Date'] >= '2023-09-04') & (data['Date'] <= '2023-09-10'))
# data_filtered = data[((data['Date'] >= '2023-08-22') & (data['Date'] <= '2023-08-24')) | ((data['Date'] >= '2023-09-04') & (data['Date'] <= '2023-09-10'))]
# data_filtered = data_filtered.reset_index(drop=True)
# data_filtered['TEMP_station'].describe()

data.head()

data[~data['HeatwavePeriod']]['TEMP_sensor'].describe()

def calculate_heat_index(T_celsius, RH_percent):
    T = T_celsius * 9/5 + 32  # Convert Celsius to Fahrenheit
    RH = RH_percent

    HI = (-42.379 + 2.04901523 * T + 10.14333127 * RH
          - 0.22475541 * T * RH - 6.83783e-3 * T**2
          - 5.481717e-2 * RH**2 + 1.22874e-3 * T**2 * RH
          + 8.5282e-4 * T * RH**2 - 1.99e-6 * T**2 * RH**2)

    return HI

data['heat_index'] = calculate_heat_index(data['TEMP_sensor'], data['HUM_sensor'])

data.groupby('HeatwavePeriod').mean()['heat_index'].describe()

data[['heat_index', 'heatIndex']]



# # Example usage
# T_c = 25  # Air temperature in Celsius
# RH = 60   # Relative humidity in percentage
# P = 101.325  # Air pressure in kPa (standard atmospheric pressure)

# humidity_ratio = southwark.calculate_humidity_ratio(T_c, RH, P) * 1000  # Convert to g/kg
# print(f"Humidity Ratio: {humidity_ratio:.4f} g of water vapor per kg of dry air")

# calculate humidity ratio
humid_ratios = [southwark.calculate_humidity_ratio(T_c, RH, p/10)*1000 for T_c, RH, p in zip(data['TEMP_sensor'], data['HUM_sensor'], data['Pressure_station'])]
data['humid_ratio'] = humid_ratios

psychrolib.SetUnitSystem(psychrolib.SI)



# Get a preconfigured chart
# chart = PsychroChart.create("minimal")
chart = PsychroChart.create("ashrae")
#chart.config.figure.figsize = (12, 8)
# Append zones:
zones_conf = {
    "zones":[{
            "zone_type": "dbt-rh",
            "style": {"edgecolor": [1.0, 0.749, 0.0, 0.8],
                      "facecolor": [1.0, 0.749, 0.0, 0.2],
                      "linewidth": 2,
                      "linestyle": "--"},
            "points_x": [23, 28],
            "points_y": [40, 60],
            "label": "Summer"
        }]}
chart.append_zones(zones_conf)

# Plot the chart
ax = chart.plot()

# Add Vertical lines
# t_min, t_opt, t_max = 16, 23, 30
# chart.plot_vertical_dry_bulb_temp_line(
#     t_min, {"color": [0.0, 0.125, 0.376], "lw": 2, "ls": ':'},
#     '  TOO COLD ({}°C)'.format(t_min), ha='left', loc=0., fontsize=14)
# chart.plot_vertical_dry_bulb_temp_line(
#     t_opt, {"color": [0.475, 0.612, 0.075], "lw": 2, "ls": ':'})
# chart.plot_vertical_dry_bulb_temp_line(
#     t_max, {"color": [1.0, 0.0, 0.247], "lw": 2, "ls": ':'},
#     'TOO HOT ({}°C)  '.format(t_max), ha='right', loc=1,
#     reverse=True, fontsize=14)

# Add labelled points and conexions between points
# points = {'heatwavePeriod': {'label': 'Heatwave Period',
#                        'style': {'color': [0.855, 0.004, 0.278, 0.01],
#                                  'marker': 'X', 'markersize': 1},
#                        'xy': (data[data['HeatwavePeriod']]['TEMP_sensor'].values, data[data['HeatwavePeriod']]['HUM_sensor'].values)},
#          'NoHeatwavePeriod': {'label': 'Non Heatwave Period',
#                        'style': {'color': [0.592, 0.745, 0.051, 0.01],
#                                  'marker': 'X', 'markersize': 1},
#                        'xy': (data[~data['HeatwavePeriod']]['TEMP_sensor'].values, data[~data['HeatwavePeriod']]['HUM_sensor'].values)},
#         #   'exterior_estimated': {
#         #       'label': 'Estimated (Weather service)',
#         #       'style': {'color': [0.573, 0.106, 0.318, 0.5],
#         #                 'marker': 'x', 'markersize': 10},
#         #       'xy': (36.7, 25.0)},
#         #   'interior': {'label': 'Interior',
#         #                'style': {'color': [0.592, 0.745, 0.051, 0.9],
#         #                          'marker': 'o', 'markersize': 30},
#         #                'xy': (29.42, 52.34)}
#         }

# chart.plot_points_dbt_rh(points)

# Add a legend
chart.plot_legend(markerscale=.7, frameon=False, fontsize=10, labelspacing=1.2)

# show as matplotlib figure
%matplotlib inline
ax.get_figure()

# generate PNG image on disk
chart.save("../figures/psychrometricChart_zone.png")


# # Get a preconfigured chart
# chart = PsychroChart()

# # Append zones:
# zones_conf = {
#     "zones":[{
#             "zone_type": "dbt-rh",
#             "style": {"edgecolor": [0.498, 0.624, 0.8],
#                       "facecolor": [0.498, 0.624, 1.0, 0.2],
#                       "linewidth": 2,
#                       "linestyle": "--"},
#             "points_x": [22, 27],
#             "points_y": [40, 60],
#             "label": "Comfort zone"
#         }]}
# chart.append_zones(zones_conf)

# # Plot the chart
# ax = chart.plot()


# chart.plot_points_dbt_rh(points={'points_series_name': (data[data['predFF']]['TEMP_sensor'].values, data[data['predFF']]['HUM_sensor'].values)}, 
#                                  scatter_style={'s': 5, 'alpha': .1, 'color': 'orange', 'marker':'X'})


# # Add a legend
# chart.plot_legend(markerscale=.7, frameon=False, fontsize=10, labelspacing=1.2)

# # show as matplotlib figure
# %matplotlib inline
# ax.get_figure()

# load data and cleaning
data = pd.read_csv('../data/mergedData_clean.csv')
data['TIME_sensor'] = pd.to_datetime(data['TIME_sensor'])
data['TIME_station'] = pd.to_datetime(data['TIME_station'])
data = data.dropna(subset=['heatIndex'])

eventList = []
for sID in tqdm(data['sensorID'].unique()):
    tempData = data[data['sensorID'] == sID].reset_index(drop=True)
    ffResult = southwark.firefinder_detector(tempData, 'TIME_sensor', 'TEMP_sensor',
                        primary_threshold=26)
    tempData['predFF'] = ffResult
    eventsDF = southwark.formatEvents(tempData, 'TIME_sensor', 'TEMP_sensor', sID, labelCol='predFF')
    eventList.append(eventsDF)

# summarize all the events
eventDF = pd.concat(eventList).reset_index(drop=True)

eventDF['start_date'] = pd.to_datetime(eventDF['start_time']).dt.date
eventDF['end_date'] = pd.to_datetime(eventDF['stop_time']).dt.date

eventDF = eventDF.rename(columns={'filename': 'sensorID'})

# export events
eventDF.to_csv('../results/firefinderEvents.csv', index=False)

data['sensorID'].unique()

dateTemp['date'].iloc[0]

# pick a sample to plot
sensor_selected = 'BL007-51'
# for sensor_selected in data['sensorID'].unique():

# first df
a = data[data['sensorID'] == sensor_selected]
display(a)
a = a[['TIME_station', 'TEMP_sensor', 'TEMP_station' ]].groupby('TIME_station').mean()
a['date']   = a.index.date

# second df
eventDF_selected = eventDF[eventDF['sensorID'] == sensor_selected]

dateTemp = a.groupby('date').max().reset_index()

fig, ax = plt.subplots(figsize=(10, 6))
sns.lineplot(data=dateTemp, x='date', y='TEMP_station', ax=ax)

# # draw a horizontal line at 26, which is the threshold for heat stress
ax.axhline(y=26, color='grey', linestyle='--')

# draw a vertical line at 2023-0901, which is the date of the event
# draw a patch between 2023-09-01 and 2023-09-11
ax.fill_between(dateTemp['date'],dateTemp['TEMP_station'].min(), dateTemp['TEMP_station'].max(), where=(dateTemp['date'] >= datetime.strptime('2023-08-22', '%Y-%m-%d').date()) & (dateTemp['date'] <= datetime.strptime('2023-08-24', '%Y-%m-%d').date()), color='red', alpha=0.1, label='Heatwave Period')
ax.fill_between(dateTemp['date'],dateTemp['TEMP_station'].min(), dateTemp['TEMP_station'].max(), where=(dateTemp['date'] >= datetime.strptime('2023-09-04', '%Y-%m-%d').date()) & (dateTemp['date'] <= datetime.strptime('2023-09-11', '%Y-%m-%d').date()), color='red', alpha=0.1, label='Heatwave Period')

for row in eventDF_selected.iterrows():
    row = row[1]
    ax.fill_between(dateTemp['date'],dateTemp['TEMP_station'].min()+5, dateTemp['TEMP_station'].max()-5, where=(dateTemp['date'] >= row['start_time'].date()) & (dateTemp['date'] <= row['stop_time'].date()), color='green', alpha=0.2, label='Indoor Heatwave Period')

sns.lineplot(data=a, x='TIME_station', y='TEMP_sensor', ax=ax)

# turn off fill_between legend
# ax.legend().remove()

# set title of the plot
ax.set_title('Heat Stress Index and Temperature of Sensor ' + sensor_selected)

# remove the grid
ax.grid(False)

plt.savefig('../figures/indoorHeatwave_' + sensor_selected + '.png', dpi=300)

plt.show()






eventDF = pd.read_csv('../results/firefinderEvents.csv')
eventDF.head()

# remove result from faulty sensor
eventDF = eventDF[eventDF['filename'] != 'BL007-52']

eventDF['start_time'] = pd.to_datetime(eventDF['start_time'])
eventDF['stop_time'] = pd.to_datetime(eventDF['stop_time'])

eventDF_10min = eventDF[eventDF['duration_mins'] >= 10]
eventDF_30min = eventDF[eventDF['duration_mins'] >= 30]
eventDF_1hr = eventDF[eventDF['duration_mins'] >= 60]
eventDF_2hr = eventDF[eventDF['duration_mins'] >= 120]

len(eventDF_10min), len(eventDF_30min), len(eventDF_1hr), len(eventDF_2hr)

eventDF_10min['duration_mins'].median(), eventDF_30min['duration_mins'].median(), eventDF_1hr['duration_mins'].median(), eventDF_2hr['duration_mins'].median()

eventDF_10min['duration_mins'].mean(), eventDF_30min['duration_mins'].mean(), eventDF_1hr['duration_mins'].mean(), eventDF_2hr['duration_mins'].mean()

eventDF_10min['filename'].nunique(), eventDF_30min['filename'].nunique(), eventDF_1hr['filename'].nunique(), eventDF_2hr['filename'].nunique()

eventDF_10min.groupby('filename').aggregate('count')['event_num'].median(), eventDF_30min.groupby('filename').aggregate('count')['event_num'].median(), eventDF_1hr.groupby('filename').aggregate('count')['event_num'].median(), eventDF_2hr.groupby('filename').aggregate('count')['event_num'].median()

eventDF_10min.groupby('filename').aggregate('count')['event_num'].mean(), eventDF_30min.groupby('filename').aggregate('count')['event_num'].mean(), eventDF_1hr.groupby('filename').aggregate('count')['event_num'].mean(), eventDF_2hr.groupby('filename').aggregate('count')['event_num'].mean()





fig, ax = plt.subplots(figsize=(20, 10))
sns.histplot(x=eventDF['start_time'].dt.hour, bins=20)
ax.spines['top'].set_visible(False)
ax.spines['right'].set_visible(False)
ax.spines['left'].set_visible(False)
ax.spines['bottom'].set_visible(False)
# make the font size of x-axis and y-axis labels larger
ax.tick_params(axis='x', labelsize=16)
ax.tick_params(axis='y', labelsize=16)
plt.show()

fig.savefig('../figures/heatwaveStart.svg', dpi=300)

fig, ax = plt.subplots(figsize=(20, 10))
sns.histplot(x=eventDF['stop_time'].dt.hour, bins=20)
ax.spines['top'].set_visible(False)
ax.spines['right'].set_visible(False)
ax.spines['left'].set_visible(False)
ax.spines['bottom'].set_visible(False)
# make the font size of x-axis and y-axis labels larger
ax.tick_params(axis='x', labelsize=16)
ax.tick_params(axis='y', labelsize=16)
plt.show()

fig.savefig('../figures/heatwaveStop.svg', dpi=300)

len(eventDF) / 26

eventDF['filename'].unique().shape

eventDF.groupby('filename').size().sort_values(ascending=False)

fig, ax = plt.subplots(figsize=(20, 10))

# sort the data by sensorID
# data = data.sort_values(by=' EPCRating')
sns.barplot(data=pd.DataFrame(eventDF.groupby('filename').size()).reset_index().rename(columns={0: 'count'}), x='filename', y='count', ax=ax)

# Rotate x-axis tick labels by 45 degrees
plt.xticks(rotation=45)

# make the font size of x-axis and y-axis labels larger
ax.tick_params(axis='x', labelsize=16)
ax.tick_params(axis='y', labelsize=16)

plt.show()

fig.savefig('../figures/miniHeatwaveBySensor.svg', dpi=300)

# detection result by EPC and house condition
hh = pd.read_csv('../data/measurement/sensorid_epc_survey_open_data.csv')
hh = hh.rename(columns={'ID': 'filename', 'Current EPC Rating ': 'EPC', 
                        'IMD Quintile': 'IMD', 'Self-report Housing Condition':'HousingCondition'})
hh.head()

eventEPC = eventDF.groupby(['filename']).size().reset_index().rename(columns={0: 'count'}).merge(hh, on='filename')
eventEPC.head(2)

eventEPC.groupby('EPC').size()

eventEPC[['EPC', 'count']].groupby('EPC').mean()

eventEPC.groupby('IMD').size()

eventEPC[['IMD', 'count']].groupby('IMD').mean()

eventEPC.groupby('HousingCondition').size()

eventEPC[['HousingCondition', 'count']].groupby('HousingCondition').mean()









hh.columns

eventDF = eventDF.merge(hh[['filename', 'IMD', 'HousingCondition', 'EPC']], on='filename', how='left')

eventDF

eventDF.groupby(CUr)





# load data and cleaning
data = pd.read_csv('../data/mergedData_clean.csv')

data['housingType'].unique()

data.columns

# data[['TEMP_sensor', 'TEMP_station', 'HUM_sensor', 'HUM_station', 'heatIndex', 'IMD']].corr()

# self rated housing condition ordinal encoding
print(data['selfRatedHousingCondition'].unique())
data['housingCondition'] = data['selfRatedHousingCondition'].map({'Very poor condition, in need of urgent repair':0, 
                                                                  'Poor condition, in need of repair':1,
                                                                  'Not new but in reasonable condition':2,
                                                                  'New and/or in good condition':3})

# EPC rating ordinal encoding
print(data[' EPCRating'].unique())
data['epcRating'] = data[' EPCRating'].map({'A':0, 'B':1, 'C':2, 'D':3, 'E':4, 'F':5})

# # dummy variables
# data = pd.get_dummies(data, columns=['housingType', 'Tenures'], drop_first=True)

# data.head()

# dropCols = ['BATT', 'LIGHT', 'NOISE_A', 'CCS811_VOCS', 'CCS811_ECO2', 
#             'PM_1', 'sensorID']

data.columns

# dataset = data[['sensorID', 'TIME_sensor', 'TEMP_sensor', 'IMD', 'TEMP_station', 'DewPoint_station', 'Temp_diff', 
#                    'Pressure_station', 'HUM_station', 'WindSpeed_station',
#                    'housingCondition', 'epcRating', 
#                 'housingType_Flat in house conversion ',
#                 'housingType_Flat/apartment (high-rise on a low floor, or low-rise)',
#                 'housingType_Flat/apartment (high-rise, on a mid to high floor)',
#                 'housingType_Terraced house',
#                 'housingType_ground first and im in the second floor.  ',
#                 'Tenures_Housing association Leaseholder with a mortgage ',
#                 'Tenures_Renting from a housing association/housing co-operative or charitable trust',
#                 'Tenures_Renting from a local authority/council',
#                 'Tenures_Renting from a private landlord', 'Tenures_Shared ownership',
#                 'Tenures_Shared ownership - part mortgage and part renting from a housing association ']]

dataset = data[['sensorID', 'TIME_sensor', 'TEMP_sensor', 'IMD', 'TEMP_station', 'DewPoint_station', 'Temp_diff', 
                'housingCondition', 'housingType', 'Tenures', 'epcRating']]

dataset.head()

dataset = dataset.dropna(subset=['TEMP_station', 'epcRating'])

# # rename the columns to replace space with underscore
# dataset.columns = dataset.columns.str.replace(' ', '_')
# dataset.columns = dataset.columns.str.replace('/', '_')
# dataset.columns = dataset.columns.str.replace('(', '')
# dataset.columns = dataset.columns.str.replace(')', '')
# dataset.columns = dataset.columns.str.replace('-', '_')
# dataset.columns = dataset.columns.str.replace(',', '')
# dataset.columns = dataset.columns.str.replace('.', '')

dataset.head(2)

# for each sensor, add a new column 





dataset.columns

# dataset['hour'] = pd.to_datetime(dataset['TIME_sensor']).dt.hour

# # 'TEMP_station', 
# X_train = dataset[['IMD', 'TEMP_station',
#                    'housingCondition', 'epcRating', 
#        'housingType_Flat_apartment_high_rise_on_a_low_floor_or_low_rise',
#        'housingType_Flat_apartment_high_rise_on_a_mid_to_high_floor',
#        'housingType_Terraced_house',
#        'housingType_ground_first_and_im_in_the_second_floor__',
#        'Tenures_Housing_association_Leaseholder_with_a_mortgage_',
#        'Tenures_Renting_from_a_housing_association_housing_co_operative_or_charitable_trust',
#        'Tenures_Renting_from_a_local_authority_council',
#        'Tenures_Renting_from_a_private_landlord', 'Tenures_Shared_ownership',
#        'Tenures_Shared_ownership___part_mortgage_and_part_renting_from_a_housing_association_', 'hour']]

# y_train = dataset['TEMP_sensor']

# X_train = X_train.apply(pd.to_numeric, errors='coerce')
# y_train = y_train.apply(pd.to_numeric, errors='coerce')


# for c in ['housingType_Flat_apartment_high_rise_on_a_low_floor_or_low_rise',
#        'housingType_Flat_apartment_high_rise_on_a_mid_to_high_floor',
#        'housingType_Terraced_house',
#        'housingType_ground_first_and_im_in_the_second_floor__',
#        'Tenures_Housing_association_Leaseholder_with_a_mortgage_',
#        'Tenures_Renting_from_a_housing_association_housing_co_operative_or_charitable_trust',
#        'Tenures_Renting_from_a_local_authority_council',
#        'Tenures_Renting_from_a_private_landlord', 'Tenures_Shared_ownership',
#        'Tenures_Shared_ownership___part_mortgage_and_part_renting_from_a_housing_association_']:
#     X_train[c] = X_train[c].astype(float)

dataset['TEMP_station']

dataset.columns

# formula = (
#     "TEMP_sensor ~ TEMP_station "
#     "+ IMD + housingCondition + epcRating "
# )

# formula = (
#     "TEMP_sensor ~ TEMP_station "
#     "+ IMD + housingCondition + epcRating "
#     "+ C(housingType)"
# )

# formula = (
#     "TEMP_sensor ~ TEMP_station "
#     "+ IMD + housingCondition + epcRating "
#     "+ C(Tenures)"
# )

formula = (
    "TEMP_sensor ~ TEMP_station "
    "+ IMD + housingCondition + epcRating "
    "+ C(Tenures) + C(housingType)"
)

model = smf.ols(formula=formula, data=dataset).fit()
print(model.summary())

# Extract coefficients and p-values
coefficients = model.params  # Exclude the first element (intercept)
p_values = model.pvalues  # Exclude the first element (intercept)

# Create a DataFrame
results_df = pd.DataFrame({
    'Variable': coefficients.index,  # Independent variable names
    'Coefficient': coefficients.values,  # Coefficient values
    'P-value': p_values.values  # P-values
})

results_df.sort_values(by='Coefficient', ascending=False)

results_df.loc[7]['Variable']

# # Add a constant to the features (for the intercept term)
# X_train_sm = sm.add_constant(X_train)

# # Fit the model
# model_sm = sm.OLS(y_train, X_train_sm).fit()

# # Summary of the model
# print(model_sm.summary())

model_sm.pvalues

# Extract coefficients and p-values
coefficients = model_sm.params[1:]  # Exclude the first element (intercept)
p_values = model_sm.pvalues[1:]  # Exclude the first element (intercept)

# Create a DataFrame
results_df = pd.DataFrame({
    'Variable': coefficients.index,  # Independent variable names
    'Coefficient': coefficients.values,  # Coefficient values
    'P-value': p_values.values  # P-values
})

results_df.sort_values(by='Coefficient', ascending=False)

# 'TEMP_station', 
numericalCols = ['IMD', 'DewPoint_station','Pressure_station', 'HUM_station', 'WindSpeed_station', 
                 'housingCondition', 'epcRating']
houseTypeCols = ['housingType_Duplex Terraced maisonette on top of a ro',
                'housingType_Flat in house conversion ',
                'housingType_Flat/apartment (high-rise on a low floor, or low-rise)',
                'housingType_Flat/apartment (high-rise, on a mid to high floor)',
                'housingType_Terraced house',
                'housingType_ground first and im in the second floor.  ',]
tenureCols = ['Tenures_Homeowner with mortgage',
            'Tenures_Housing association Leaseholder with a mortgage ',
            'Tenures_Renting from a housing association/housing co-operative or charitable trust',
            'Tenures_Renting from a local authority/council',
            'Tenures_Renting from a private landlord', 'Tenures_Shared ownership',
            'Tenures_Shared ownership - part mortgage and part renting from a housing association ']

results_df[results_df['Variable'].isin(numericalCols)]['Variable']

sns.set_theme(style="whitegrid")

fig, ax = plt.subplots(figsize=(6, 6))
sns.barplot(data=results_df[results_df['Variable'].isin(numericalCols)], x='Coefficient', y='Variable', ax=ax, linewidth=0)
# Remove all spines (borders)
ax.spines['top'].set_visible(False)
ax.spines['right'].set_visible(False)
ax.spines['left'].set_visible(False)
ax.spines['bottom'].set_visible(False)
plt.show()

fig.savefig('../figures/numericalRegression.png', dpi=300, bbox_inches='tight')

fig, ax = plt.subplots(figsize=(6, 6))
sns.barplot(data=results_df[results_df['Variable'].isin(houseTypeCols)], x='Coefficient', y='Variable', ax=ax, linewidth=0)
# Remove all spines (borders)
ax.spines['top'].set_visible(False)
ax.spines['right'].set_visible(False)
ax.spines['left'].set_visible(False)
ax.spines['bottom'].set_visible(False)
plt.show()

fig, ax = plt.subplots(figsize=(6, 6))
sns.barplot(data=results_df[results_df['Variable'].isin(tenureCols)], x='Coefficient', y='Variable', ax=ax, linewidth=0)
# Remove all spines (borders)
ax.spines['top'].set_visible(False)
ax.spines['right'].set_visible(False)
ax.spines['left'].set_visible(False)
ax.spines['bottom'].set_visible(False)
plt.show()

dataset.head(2)

dataset.columns

dataset = dataset.drop(columns=['housingType_Flat_in_house_conversion_'])

df = dataset.copy()

# Add hour-of-day and day-index columns
df['hour'] = pd.to_datetime(df['TIME_sensor']).dt.hour          # 0 … 23

# Example of other time-invariant covariates
# (drop or rename to match your data)
time_invariant = ['IMD', 'epcRating', 'housingCondition', 
       'housingType_Flat_apartment_high_rise_on_a_low_floor_or_low_rise',
       'housingType_Flat_apartment_high_rise_on_a_mid_to_high_floor',
       'housingType_Terraced_house',
       'housingType_ground_first_and_im_in_the_second_floor__',
       'Tenures_Housing_association_Leaseholder_with_a_mortgage_',
       'Tenures_Renting_from_a_housing_association_housing_co_operative_or_charitable_trust',
       'Tenures_Renting_from_a_local_authority_council',
       'Tenures_Renting_from_a_private_landlord', 'Tenures_Shared_ownership',
       'Tenures_Shared_ownership___part_mortgage_and_part_renting_from_a_housing_association_']

formula = (
    "Temp_diff ~ TEMP_station "
    "+ " + " + ".join(time_invariant) + " "
    "+ C(hour)"
)
# formula = (
#     "Temp_diff ~ C(hour) "
#     "+ " + " + ".join(time_invariant)
# )

formula

zero_var = [c for c in df.columns if df[c].nunique() == 1]
print("constant columns:", zero_var)

md = smf.mixedlm(formula, data=df, groups=df["sensorID"])
m_fit = md.fit(reml=False)                # ML; use reml=True if you prefer REML
print(m_fit.summary())

# Extract coefficients and p-values
coefficients = m_fit.params  # Exclude the first element (intercept)
p_values = m_fit.pvalues  # Exclude the first element (intercept)

# Create a DataFrame
results_df = pd.DataFrame({
    'Variable': coefficients.index,  # Independent variable names
    'Coefficient': coefficients.values,  # Coefficient values
    'P-value': p_values.values  # P-values
})

m_fit.cov_re

robust = m_fit.get_robustcov_results(cov_type="cluster",
                                 cov_kwds={"groups": df["sensorID"]})

pd.set_option('display.float_format', lambda x: '%.3f' % x)
results_df







# # Create a horizontal bar plot
# # Width of bars = coefficients, Height of bars = p-values
# bar_trace = go.Bar(
#     y=results_df[results_df['Variable'].isin(numericalCols)]['Variable'],  # Variables on the y-axis
#     x=results_df[results_df['Variable'].isin(numericalCols)]['Coefficient'],    # Width of bars = coefficients
#     orientation='h',   # Horizontal bar plot
#     marker=dict(
#         color=p_values,  # Color bars by p-values
#         colorscale='Viridis',  # Use a color scale for p-values
#         colorbar=dict(title='P-Value'),  # Add a color bar for p-values
#         line=dict(color='black', width=1)  # Add borders to bars
#     ),
#     width=0.6,  # Adjust bar width for better visualization
#     name='Coefficients (Width) and P-Values (Color)'
# )

# # Create the layout
# layout = go.Layout(
#     title='Regression Coefficients (Width) and P-Values (Color)',
#     xaxis=dict(title='Coefficients'),
#     yaxis=dict(title='Variables'),
#     showlegend=True,
#     bargap=0.2  # Adjust gap between bars
# )

# # Create the figure
# fig = go.Figure(data=[bar_trace], layout=layout)

# # Show the figure
# fig.show()

from statsmodels.stats.outliers_influence import variance_inflation_factor

# Calculate VIF for each independent variable
vif_data = pd.DataFrame()
vif_data['Variable'] = X_train_sm.columns
vif_data['VIF'] = [variance_inflation_factor(X_train_sm.values, i) for i in range(X_train_sm.shape[1])]

vif_data





