import math
import numpy as np
import rle
import pandas as pd
import copy



### Help functions for firefinder algorithm

def runQuantile(col, k, prob=0.8):
    quantile_difftemps = []
    for i in range(len(col)):
        if i <= (len(col)-k):
            quantile_difftemps.append(np.quantile(col[i:(i+k)], 0.8))
    quantile_difftemps.extend([None] * (k-1))
    return quantile_difftemps

def smoothEvents(label, sample_interval, min_event_sec = 5*60, min_break_sec = 30 * 60):
    #remove short breaks between cooking
    rleLabel1, rleLength1 = rle.encode(label)
    rleDF1 = pd.DataFrame(np.transpose([rleLabel1, rleLength1]), columns=['label', 'length'])
    selectedRows = (rleDF1['label'] == 0) & (rleDF1['length']*sample_interval < min_break_sec)
    rleDF1.loc[selectedRows.to_list(), 'label'] = 1
    rleDF1['label'] = rleDF1['label'].astype(bool)

    newLabels1 = []
    for idx, row in rleDF1.iterrows():
        newLabels1.extend([row['label']] * row['length'])
    
    #remove short cooking events
    rleLabel2, rleLength2 = rle.encode(newLabels1)

    rleDF2 = pd.DataFrame(np.transpose([rleLabel2, rleLength2]), columns=['label', 'length'])
    selectedRows = ((rleDF2['label'] == 1) & (rleDF2['length']*sample_interval < min_event_sec)) | ((rleDF2['label'] == 1) & (rleDF2['length'] < 2))
    rleDF2.loc[selectedRows, 'label'] = 0
    rleDF2['label'] = rleDF2['label'].astype(bool)
    
    newLabels2 = []
    for idx, row in rleDF2.iterrows():
        newLabels2.extend([row['label']] * row['length'])
        
    return newLabels2

def firefinder_detector(data, timeColName, tempColName,
                        primary_threshold=75,
                        min_event_temp=None, 
                        min_event_sec=5*60, 
                        min_break_sec=30*60):
    #' Detect Cooking Using Firefinder Algorithm
    #' 
    #' TODO: Danny document this 
    #' 
    #' @param data a sumsarizer formatted data table for one sensor mission
    #' @param primary_threshold the main threshold to determine cooking
    #' @param min_event_temp min temperature for an event
    #' @param min_event_sec min number of seconds in a real event
    #' @param min_break_sec min number of seconds to makeup a real break
    max_run_length = 100
    
    # calculate features
#     sample_interval = get_sample_interval(data)
    sample_interval = 60
    sample_interval_mins = sample_interval/60
    
    dataCopy = copy.deepcopy(data)
    dataCopy[timeColName] = pd.to_datetime(dataCopy[timeColName])
    #make a column of 1st derivative (degC/minute)
    dataCopy['difftemps'] = dataCopy[tempColName].diff()
    
    #make a column of delta timestamps
    dataCopy['difftimes'] = pd.to_datetime(dataCopy[timeColName]).diff() / np.timedelta64(1, 's')
    
    #look at whether or not most of the data coming up in the next
    #hour is negative slope or 100 data points, whichever is lower
    dataCopy['quantile_difftemps'] = runQuantile(dataCopy['difftemps'], 
                                                 k=min(max_run_length, round(60/sample_interval_mins)))
    
    ##### RUN THE DECISION TREE 
    
    #define points that are likely to be cooking
    dataCopy['label'] = dataCopy[tempColName]>primary_threshold
    
    #get rid of long runs of negative slopes
    selectedRows = dataCopy['quantile_difftemps'] < 0
    dataCopy.loc[selectedRows, 'label'] = False
    
    #assume cooking for highly positive slopes
    selectedRows = dataCopy['difftemps'] > 2
    dataCopy.loc[selectedRows, 'label'] = True
    
    #get rid of highly negative slopes
    selectedRows = [d < (-1*v/500) for d,v in zip(dataCopy['difftemps'], dataCopy[tempColName])]
    dataCopy.loc[selectedRows, 'label'] = False
    
    #remove places with gaps longer than the sample interval
    selectedRows = dataCopy['difftimes'] > sample_interval
    dataCopy.loc[selectedRows, 'label'] = False
    
    
    dataCopy['label'] = smoothEvents(dataCopy['label'], sample_interval, min_event_sec, min_break_sec)
    dataCopy['label'] = smoothEvents(dataCopy['label'], sample_interval, min_event_sec, min_break_sec)
#     dataCopy['label'] = labelNew
    
    #remove events with very low cooking temps
    if min_event_temp != None:
        selectedRows = dataCopy[tempColName] < min_event_temp
        dataCopy.loc[selectedRows, 'label'] = False
    
    #remove events for data that is out of range and is probably an error
    selectedRows = (dataCopy[tempColName] > 1000) | (dataCopy[tempColName] < -50)
    dataCopy.loc[selectedRows, 'label'] = False
    
    return dataCopy['label']

def formatEvents(data, timeColName, tempColName, filePath, labelCol='predML'):
    rleLabel1, rleLength1 = rle.encode(data[labelCol])

    count = 1
    events = []
    for i in range(len(rleLabel1)):
      if rleLabel1[i]:
          startIdx = sum(rleLength1[:i])
          endIdx = sum(rleLength1[:i+1])
          tempDF = data.iloc[startIdx:endIdx]
          t = pd.to_datetime(tempDF[timeColName])
    #         .diff() / np.timedelta64(1, 's')
          events.append({
              'event_num': count, 
              'start_time': tempDF[timeColName].iloc[0], 
              'stop_time': tempDF[timeColName].iloc[-1], 
              'min_temp': tempDF[tempColName].min(), 
              'max_temp': tempDF[tempColName].max(), 
              'duration_mins': (t.iloc[-1] - t.iloc[0]) / np.timedelta64(1, 's') / 60
          })
          count += 1  
    eventsDF = pd.DataFrame(events)
    eventsDF['filename'] = filePath.split('/')[-1].split('.')[0]
    return eventsDF

def calculate_humidity_ratio(T_c, RH, P=101.325):
    """
    Calculate the humidity ratio given the relative humidity, air temperature, and pressure.
    
    Parameters:
    T_c (float): Air temperature in Celsius
    RH (float): Relative humidity in percentage
    P (float): Air pressure in kPa
    
    Returns:
    float: Humidity ratio (kg of water vapor per kg of dry air)
    """
    # Convert air temperature to Kelvin
    T_k = T_c + 273.15
    
    # Calculate the saturation vapor pressure (E_s) using Tetens formula
    E_s = 0.6108 * math.exp((17.27 * T_c) / (T_c + 237.3))
    
    # Calculate the actual vapor pressure (E)
    E = (RH / 100.0) * E_s
    
    # Calculate the humidity ratio (W)
    W = 0.622 * E / (P - E)
    
    return W